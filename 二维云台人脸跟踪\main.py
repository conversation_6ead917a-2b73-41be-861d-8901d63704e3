from maix import camera, display, image, nn, app
import math
import servo

INIT_POS_X = 90
INIT_POS_Y = 100
FILTER_FACTOR = 0.15

detector = nn.Retinaface(model="/root/models/retinaface.mud")
cam = camera.Camera(detector.input_width(), detector.input_height(), detector.input_format())
dis = display.Display()

servo_x = servo.Servo(6, INIT_POS_X)
servo_y = servo.Servo(7, INIT_POS_Y)

target_x_pos = INIT_POS_X
target_y_pos = INIT_POS_Y

last_err_x_pos = 0
last_err_y_pos = 0

image_width  = detector.input_width()
image_height = detector.input_height()

while not app.need_exit():
    img = cam.read()
    objs = detector.detect(img, conf_th = 0.4, iou_th = 0.45)
    if len(objs) == 0: #没有找到人脸时，仅显示，不做追踪处理
        dis.show(img)
        continue
    else:
        obj = objs[0]
        img.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED)
        # print('{}.{}.{}.{}'.format(obj.x, obj.y, obj.w, obj.h))

        #取得横向舵机位置偏差，并进行一阶滞后滤波
        err_x_pos = image_width/2 - (obj.x+obj.w/2)
        err_x_pos = FILTER_FACTOR*err_x_pos + (1-FILTER_FACTOR)*last_err_x_pos

        #增量PID 计算目标控制量(仅用到PI控制)
        delta_x_pos = 0.2*(err_x_pos-last_err_x_pos) + 0.018*err_x_pos
        last_err_x_pos = err_x_pos
        target_x_pos += delta_x_pos
    
        #取得纵向舵机位置偏差，并进行一阶滞后滤波
        err_y_pos = image_height/2 - (obj.y+obj.h/2)
        err_y_pos = FILTER_FACTOR*err_y_pos + (1-FILTER_FACTOR)*last_err_y_pos

        #增量PID 计算目标控制量(仅用到PI控制)
        delta_y_pos = 0.2*(err_y_pos-last_err_y_pos) + 0.018*err_y_pos
        last_err_y_pos = err_y_pos
        target_y_pos += delta_y_pos

        #控制舵机角度
        servo_x.angle(target_x_pos)
        servo_y.angle(target_y_pos)

        dis.show(img)
