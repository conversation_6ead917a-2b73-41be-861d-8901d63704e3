# 🎯 二维云台人脸跟踪系统 - 小白完全指南

## 📖 项目简介

这是一个基于MaixCam的智能人脸跟踪系统，能够自动识别人脸并控制二维云台（可以上下左右转动的摄像头支架）跟随人脸移动。就像一个会"看人"的智能摄像头！

## 🎬 系统工作原理

想象一下：
1. 📷 摄像头不断拍照
2. 🧠 AI大脑识别照片中的人脸
3. 🎯 计算人脸位置偏离中心多远
4. ⚙️ 控制舵机转动，让摄像头对准人脸
5. 🔄 重复以上过程，实现实时跟踪

## 📁 文件结构

```
项目文件夹/
├── main.py          # 主程序文件（核心逻辑）
└── servo.py         # 舵机控制模块（硬件驱动）
```

## 🔧 硬件组成

- **MaixCam开发板**：运行AI算法的"大脑"
- **摄像头**：获取图像的"眼睛"
- **显示屏**：显示图像的"屏幕"
- **两个舵机**：
  - servo_x (PWM6)：控制水平转动（左右）
  - servo_y (PWM7)：控制垂直转动（上下）

## 📋 详细代码解析

### 1. 导入必要的库
```python
from maix import camera, display, image, nn, app
import math
import servo
```
**解释**：就像准备工具一样，导入摄像头、显示器、图像处理、神经网络等功能模块。

### 2. 重要参数设置
```python
INIT_POS_X = 90      # 水平舵机初始角度（正中间）
INIT_POS_Y = 100     # 垂直舵机初始角度
FILTER_FACTOR = 0.15 # 滤波系数（让动作更平滑）
```
**解释**：
- `INIT_POS_X/Y`：舵机开机时的"默认姿势"
- `FILTER_FACTOR`：数值越小，动作越平滑但反应越慢

### 3. 初始化硬件设备
```python
detector = nn.Retinaface(model="/root/models/retinaface.mud")  # AI人脸检测器
cam = camera.Camera(...)      # 摄像头
dis = display.Display()       # 显示屏
servo_x = servo.Servo(6, INIT_POS_X)  # 水平舵机
servo_y = servo.Servo(7, INIT_POS_Y)  # 垂直舵机
```

### 4. 核心跟踪算法

#### 🎯 人脸检测
```python
objs = detector.detect(img, conf_th = 0.4, iou_th = 0.45)
```
**参数说明**：
- `conf_th = 0.4`：置信度阈值，只有40%以上把握是人脸才认定
- `iou_th = 0.45`：重叠度阈值，用于去除重复检测

#### 📐 位置偏差计算
```python
err_x_pos = image_width/2 - (obj.x+obj.w/2)   # 水平偏差
err_y_pos = image_height/2 - (obj.y+obj.h/2)  # 垂直偏差
```
**原理**：
- 图像中心：`image_width/2, image_height/2`
- 人脸中心：`obj.x+obj.w/2, obj.y+obj.h/2`
- 偏差 = 图像中心 - 人脸中心

#### 🔄 一阶滞后滤波
```python
err_x_pos = FILTER_FACTOR*err_x_pos + (1-FILTER_FACTOR)*last_err_x_pos
```
**作用**：让舵机动作更平滑，避免抖动
**原理**：新值 = 15%当前值 + 85%历史值

#### ⚡ PID控制算法
```python
delta_x_pos = 0.2*(err_x_pos-last_err_x_pos) + 0.018*err_x_pos
target_x_pos += delta_x_pos
```
**参数解释**：
- `0.2`：比例系数P，控制反应速度
- `0.018`：积分系数I，消除稳态误差
- 没有D项（微分），因为会增加噪声

## 🎛️ 舵机控制模块 (servo.py)

### 核心参数
```python
SERVO_FREQ = 50        # 频率50Hz（每20ms一个周期）
SERVO_MIN_DUTY = 2.5   # 最小占空比2.5%（对应0度）
SERVO_MAX_DUTY = 12.5  # 最大占空比12.5%（对应180度）
```

### 角度转换公式
```python
duty = (12.5 - 2.5) / 180 * angle + 2.5
```
**原理**：将0-180度线性映射到2.5%-12.5%占空比

## 🚀 运行流程

1. **初始化**：设置摄像头、显示器、舵机到初始位置
2. **主循环**：
   - 📸 拍摄一帧图像
   - 🔍 检测人脸
   - ❌ 没有人脸：仅显示图像
   - ✅ 发现人脸：
     - 🎯 计算位置偏差
     - 🔄 滤波处理
     - ⚙️ PID控制计算
     - 🎮 控制舵机转动
     - 📺 显示带框的图像

## 🎨 参数调优指南

### 🎯 检测参数
- `conf_th`：降低值检测更敏感，提高值减少误检
- `iou_th`：影响重复检测的过滤

### 🔄 滤波参数
- `FILTER_FACTOR`：
  - 增大：反应更快，但可能抖动
  - 减小：更平滑，但反应较慢

### ⚡ PID参数
- **P系数 (0.2)**：
  - 增大：反应更快，可能超调
  - 减小：反应较慢，更稳定
- **I系数 (0.018)**：
  - 增大：消除误差更快，可能振荡
  - 减小：更稳定，但可能有残余误差

## 🛠️ 常见问题解决

### 问题1：跟踪不稳定，一直抖动
**解决方案**：
- 减小`FILTER_FACTOR`值（如改为0.1）
- 减小PID的P系数

### 问题2：反应太慢，跟不上人脸移动
**解决方案**：
- 增大`FILTER_FACTOR`值（如改为0.3）
- 增大PID的P系数

### 问题3：检测不到人脸
**解决方案**：
- 降低`conf_th`值（如改为0.3）
- 检查光线条件
- 确保人脸大小适中

## 🎓 学习建议

1. **理解基础概念**：先搞懂什么是PID控制、滤波器
2. **动手实验**：修改参数观察效果变化
3. **逐步优化**：一次只改一个参数，观察影响
4. **记录结果**：记录不同参数组合的效果

## 🔗 扩展功能建议

- 🎯 多人脸跟踪：选择最大或最近的人脸
- 📊 添加跟踪精度显示
- 🎮 手动控制模式
- 📹 录制跟踪视频
- 🔊 语音提示功能

---
**💡 小贴士**：这个项目结合了计算机视觉、控制理论和嵌入式开发，是学习AI应用的绝佳案例！
